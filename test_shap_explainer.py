#!/usr/bin/env python3
"""
测试SHAP预测解释系统
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.fusion.prediction_explainer import PredictionExplainer

def test_shap_explainer():
    """测试SHAP解释器"""
    print("=== 测试SHAP预测解释系统 ===")
    
    try:
        # 1. 初始化解释器
        print("1. 初始化SHAP解释器...")
        explainer = PredictionExplainer()
        print("✓ SHAP解释器初始化成功")
        
        # 2. 检查状态
        print("2. 检查解释器状态...")
        status = explainer.get_explainer_status()
        print(f"✓ SHAP可用: {status['shap_available']}")
        print(f"✓ 特征接口数量: {status['feature_interfaces_count']}")
        print(f"✓ 支持位置: {status['positions']}")
        
        # 3. 测试备用解释功能
        print("3. 测试备用解释功能...")
        fallback_explanation = explainer._generate_fallback_explanation(
            'hundreds', 5, 0.75
        )
        print(f"✓ 备用解释生成成功")
        print(f"  预测数字: {fallback_explanation['predicted_number']}")
        print(f"  置信度: {fallback_explanation['confidence']:.1%}")
        print(f"  解释: {fallback_explanation['overall_explanation']}")
        
        # 4. 测试特征名称转换
        print("4. 测试特征名称转换...")
        test_features = ['hundreds_frequency_5', 'tens_missing_10', 'units_trend_20']
        for feature in test_features:
            readable = explainer._make_feature_readable(feature)
            print(f"  {feature} -> {readable}")
        
        # 5. 测试融合结果解释
        print("5. 测试融合结果解释...")
        mock_fusion_result = {
            'fusion_method': 'adaptive_fusion',
            'predictions': [
                {'hundreds': 1, 'tens': 2, 'units': 3, 'probability': 0.85},
                {'hundreds': 4, 'tens': 5, 'units': 6, 'probability': 0.78},
                {'hundreds': 7, 'tens': 8, 'units': 9, 'probability': 0.72}
            ]
        }
        
        fusion_explanation = explainer.explain_fusion_result(mock_fusion_result)
        print(f"✓ 融合解释生成成功")
        print(f"  融合方法: {fusion_explanation['fusion_method']}")
        print(f"  总预测数: {fusion_explanation['total_predictions']}")
        print(f"  解释摘要: {fusion_explanation['summary']}")
        
        for pred in fusion_explanation['explained_predictions']:
            print(f"  排名{pred['rank']}: {pred['combination']} (概率: {pred['probability']:.1%})")
        
        print("\n=== SHAP解释系统测试完成 ===")
        print("✓ 所有基础功能测试通过")
        print("📝 注意: 完整的SHAP解释需要训练好的模型")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_shap_explainer()
    sys.exit(0 if success else 1)
