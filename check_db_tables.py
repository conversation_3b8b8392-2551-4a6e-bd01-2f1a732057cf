#!/usr/bin/env python3
"""
检查数据库表结构
"""

import sqlite3
import pandas as pd

def check_database():
    """检查数据库表结构"""
    try:
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("=== 数据库表列表 ===")
        for table in tables:
            print(f"- {table[0]}")
        
        # 检查主要数据表的结构
        main_tables = ['fusion_predictions', 'final_predictions', 'prediction_records']
        
        for table_name in main_tables:
            try:
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                if columns:
                    print(f"\n=== {table_name} 表结构 ===")
                    for col in columns:
                        print(f"  {col[1]} ({col[2]})")
                    
                    # 查看数据样例
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3;")
                    rows = cursor.fetchall()
                    if rows:
                        print(f"\n=== {table_name} 数据样例 ===")
                        for i, row in enumerate(rows):
                            print(f"  行{i+1}: {row}")
                    break
            except Exception as e:
                print(f"表 {table_name} 不存在或查询失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库连接失败: {e}")

if __name__ == "__main__":
    check_database()
