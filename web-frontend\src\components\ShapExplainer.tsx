import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Select,
  Input,
  Button,
  Table,
  Alert,
  Spin,
  Typography,
  Space,
  Tag,
  Progress,
  Divider,
  message,
  Tabs
} from 'antd'
import {
  ExperimentOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  B<PERSON>bOutlined,
  Check<PERSON>ircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { apiRequest } from '../utils/api'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface ShapStatus {
  shap_available: boolean
  explainers_count: number
  feature_interfaces_count: number
  positions: string[]
  status: string
  message: string
}

interface FeatureImportance {
  feature_name: string
  importance: number
  rank: number
}

interface PredictionExplanation {
  prediction_number: string
  position: string
  model_type: string
  explanation: {
    feature_contributions: FeatureImportance[]
    prediction_confidence: number
    top_features: string[]
  }
}

const ShapExplainer: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [shapStatus, setShapStatus] = useState<ShapStatus | null>(null)
  const [predictionNumber, setPredictionNumber] = useState('')
  const [selectedPosition, setSelectedPosition] = useState<string>('hundreds')
  const [selectedModel, setSelectedModel] = useState<string>('xgboost')
  const [explanation, setExplanation] = useState<PredictionExplanation | null>(null)
  const [featureImportance, setFeatureImportance] = useState<FeatureImportance[]>([])
  const [availableModels, setAvailableModels] = useState<string[]>([])

  // 获取SHAP状态
  const fetchShapStatus = async () => {
    try {
      const response = await apiRequest('/api/shap/status')
      setShapStatus(response.data)
    } catch (error) {
      console.error('获取SHAP状态失败:', error)
      message.error('获取SHAP状态失败')
    }
  }

  // 获取可用模型
  const fetchAvailableModels = async () => {
    try {
      const response = await apiRequest('/api/shap/models/available')
      setAvailableModels(response.data.models || [])
    } catch (error) {
      console.error('获取可用模型失败:', error)
    }
  }

  // 解释单个预测
  const explainPrediction = async () => {
    if (!predictionNumber || predictionNumber.length !== 3) {
      message.error('请输入3位数字的预测号码')
      return
    }

    setLoading(true)
    try {
      const response = await apiRequest('/api/shap/explain/prediction', {
        method: 'POST',
        params: {
          prediction_number: predictionNumber,
          position: selectedPosition,
          model_type: selectedModel
        }
      })
      setExplanation(response.data)
      message.success('预测解释生成成功')
    } catch (error) {
      console.error('预测解释失败:', error)
      message.error('预测解释失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取特征重要性
  const fetchFeatureImportance = async () => {
    setLoading(true)
    try {
      const response = await apiRequest(`/api/shap/explain/features/${selectedPosition}`, {
        params: {
          model_type: selectedModel,
          top_n: 10
        }
      })
      setFeatureImportance(response.data.feature_importance || [])
    } catch (error) {
      console.error('获取特征重要性失败:', error)
      message.error('获取特征重要性失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchShapStatus()
    fetchAvailableModels()
  }, [])

  useEffect(() => {
    if (selectedPosition) {
      fetchFeatureImportance()
    }
  }, [selectedPosition, selectedModel])

  // 特征重要性表格列
  const featureColumns = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 80,
      render: (rank: number) => (
        <Tag color={rank <= 3 ? 'gold' : rank <= 5 ? 'blue' : 'default'}>
          #{rank}
        </Tag>
      )
    },
    {
      title: '特征名称',
      dataIndex: 'feature_name',
      key: 'feature_name',
    },
    {
      title: '重要性',
      dataIndex: 'importance',
      key: 'importance',
      render: (importance: number) => (
        <div>
          <Progress 
            percent={Math.round(importance * 100)} 
            size="small" 
            status={importance > 0.1 ? 'active' : 'normal'}
          />
          <Text type="secondary">{(importance * 100).toFixed(2)}%</Text>
        </div>
      )
    }
  ]

  const positionOptions = [
    { value: 'hundreds', label: '百位' },
    { value: 'tens', label: '十位' },
    { value: 'units', label: '个位' }
  ]

  return (
    <div>
      <Title level={2}>
        <ExperimentOutlined /> SHAP预测解释
      </Title>
      
      {/* SHAP状态卡片 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              {shapStatus?.shap_available ? (
                <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
              ) : (
                <ExclamationCircleOutlined style={{ fontSize: 48, color: '#ff4d4f' }} />
              )}
              <div style={{ marginTop: 8 }}>
                <Text strong>
                  {shapStatus?.shap_available ? 'SHAP可用' : 'SHAP不可用'}
                </Text>
              </div>
            </div>
          </Col>
          <Col span={18}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div>
                <Text strong>状态: </Text>
                <Tag color={shapStatus?.status === 'available' ? 'green' : 'red'}>
                  {shapStatus?.message || '未知'}
                </Tag>
              </div>
              <div>
                <Text strong>支持位置: </Text>
                {shapStatus?.positions?.map(pos => (
                  <Tag key={pos} color="blue">
                    {pos === 'hundreds' ? '百位' : pos === 'tens' ? '十位' : '个位'}
                  </Tag>
                ))}
              </div>
              <div>
                <Text strong>特征接口数量: </Text>
                <Text>{shapStatus?.feature_interfaces_count || 0}</Text>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      <Tabs defaultActiveKey="1">
        <TabPane tab={<span><BulbOutlined />单个预测解释</span>} key="1">
          <Card>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Text strong>预测号码:</Text>
                <Input
                  placeholder="输入3位数字"
                  value={predictionNumber}
                  onChange={(e) => setPredictionNumber(e.target.value)}
                  maxLength={3}
                />
              </Col>
              <Col span={6}>
                <Text strong>预测位置:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedPosition}
                  onChange={setSelectedPosition}
                >
                  {positionOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <Text strong>模型类型:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedModel}
                  onChange={setSelectedModel}
                >
                  {availableModels.map(model => (
                    <Option key={model} value={model}>
                      {model.toUpperCase()}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <Button
                  type="primary"
                  icon={<ExperimentOutlined />}
                  onClick={explainPrediction}
                  loading={loading}
                  disabled={!shapStatus?.shap_available}
                  style={{ marginTop: 22 }}
                >
                  解释预测
                </Button>
              </Col>
            </Row>

            {explanation && (
              <div style={{ marginTop: 24 }}>
                <Divider>预测解释结果</Divider>
                <Alert
                  message={`预测号码 ${explanation.prediction_number} 的 ${
                    explanation.position === 'hundreds' ? '百位' : 
                    explanation.position === 'tens' ? '十位' : '个位'
                  } 解释`}
                  description={`使用 ${explanation.model_type.toUpperCase()} 模型生成的解释`}
                  type="info"
                  style={{ marginBottom: 16 }}
                />
                
                {explanation.explanation && (
                  <Row gutter={16}>
                    <Col span={12}>
                      <Card title="预测置信度" size="small">
                        <Progress
                          type="circle"
                          percent={Math.round((explanation.explanation.prediction_confidence || 0) * 100)}
                          format={percent => `${percent}%`}
                        />
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card title="关键特征" size="small">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          {explanation.explanation.top_features?.map((feature, index) => (
                            <Tag key={index} color="blue">{feature}</Tag>
                          ))}
                        </Space>
                      </Card>
                    </Col>
                  </Row>
                )}
              </div>
            )}
          </Card>
        </TabPane>

        <TabPane tab={<span><BarChartOutlined />特征重要性</span>} key="2">
          <Card>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Text strong>分析位置:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedPosition}
                  onChange={setSelectedPosition}
                >
                  {positionOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={8}>
                <Text strong>模型类型:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedModel}
                  onChange={setSelectedModel}
                >
                  {availableModels.map(model => (
                    <Option key={model} value={model}>
                      {model.toUpperCase()}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={8}>
                <Button
                  type="primary"
                  icon={<BarChartOutlined />}
                  onClick={fetchFeatureImportance}
                  loading={loading}
                  disabled={!shapStatus?.shap_available}
                  style={{ marginTop: 22 }}
                >
                  刷新分析
                </Button>
              </Col>
            </Row>

            <Spin spinning={loading}>
              <Table
                columns={featureColumns}
                dataSource={featureImportance}
                rowKey="feature_name"
                pagination={false}
                size="small"
              />
            </Spin>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  )
}

export default ShapExplainer
