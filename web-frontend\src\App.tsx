import React, { useState, Suspense, lazy } from 'react'
import { Layout, Menu, theme, Badge, Spin } from 'antd'
import {
  DashboardOutlined,
  Bar<PERSON><PERSON>Outlined,
  MonitorOutlined,
  SettingOutlined,
  TrophyOutlined,
  ToolOutlined,
  FileSearchOutlined,
  ExperimentOutlined,
} from '@ant-design/icons'
import WebSocketStatus from './components/WebSocketStatus'
import RequestMonitor from './components/RequestMonitor'
import PollingMonitor from './components/PollingMonitor'
import { useWebSocket } from './hooks/useWebSocket'
import './App.css'

// 懒加载组件
const Dashboard = lazy(() => import('./components/Dashboard'))
const SystemMonitor = lazy(() => import('./components/SystemMonitor'))
const PerformanceMetrics = lazy(() => import('./components/PerformanceMetrics'))
const OptimizationTasks = lazy(() => import('./components/OptimizationTasks'))
const HistoryAnalysis = lazy(() => import('./components/HistoryAnalysis'))
const SystemSettings = lazy(() => import('./components/SystemSettings'))
const OptimizationControl = lazy(() => import('./components/OptimizationControl'))
const DiagnosticsPanel = lazy(() => import('./components/DiagnosticsPanel'))
const ReviewPanel = lazy(() => import('./components/ReviewPanel'))
const ShapExplainer = lazy(() => import('./components/ShapExplainer'))

const { Header, Content, Sider } = Layout

const App: React.FC = () => {
  const [selectedKey, setSelectedKey] = useState('1')
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken()

  // WebSocket连接状态
  const { status: wsStatus, reconnect: reconnectWS } = useWebSocket({
    autoReconnect: true
  })

  const menuItems = [
    {
      key: '1',
      icon: <DashboardOutlined />,
      label: '预测仪表板',
    },
    {
      key: '2',
      icon: <BarChartOutlined />,
      label: '历史分析',
    },
    {
      key: '9',
      icon: <FileSearchOutlined />,
      label: '复盘分析',
    },
    {
      key: '10',
      icon: <ExperimentOutlined />,
      label: 'SHAP解释',
    },
    {
      key: '3',
      icon: <MonitorOutlined />,
      label: 'P9系统监控',
    },
    {
      key: '4',
      icon: <TrophyOutlined />,
      label: '优化任务',
    },
    {
      key: '6',
      icon: <BarChartOutlined />,
      label: '性能指标',
    },
    {
      key: '7',
      icon: <SettingOutlined />,
      label: '优化控制',
    },
    {
      key: '8',
      icon: <ToolOutlined />,
      label: '系统诊断',
    },
    {
      key: '5',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ]

  const renderContent = () => {
    const LoadingSpinner = (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>正在加载页面...</p>
      </div>
    )

    switch (selectedKey) {
      case '1':
        return <Suspense fallback={LoadingSpinner}><Dashboard /></Suspense>
      case '2':
        return <Suspense fallback={LoadingSpinner}><HistoryAnalysis /></Suspense>
      case '3':
        return <Suspense fallback={LoadingSpinner}><SystemMonitor /></Suspense>
      case '4':
        return <Suspense fallback={LoadingSpinner}><OptimizationTasks /></Suspense>
      case '5':
        return <Suspense fallback={LoadingSpinner}><SystemSettings /></Suspense>
      case '6':
        return <Suspense fallback={LoadingSpinner}><PerformanceMetrics /></Suspense>
      case '7':
        return <Suspense fallback={LoadingSpinner}><OptimizationControl /></Suspense>
      case '8':
        return <Suspense fallback={LoadingSpinner}><DiagnosticsPanel /></Suspense>
      case '9':
        return <Suspense fallback={LoadingSpinner}><ReviewPanel /></Suspense>
      case '10':
        return <Suspense fallback={LoadingSpinner}><ShapExplainer /></Suspense>
      default:
        return <Suspense fallback={LoadingSpinner}><Dashboard /></Suspense>
    }
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider collapsible>
        <div className="demo-logo-vertical" />
        <div style={{ 
          height: 32, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.2)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold'
        }}>
          福彩3D预测
        </div>
        <Menu
          theme="dark"
          defaultSelectedKeys={['1']}
          selectedKeys={[selectedKey]}
          mode="inline"
          items={menuItems}
          onClick={({ key }) => setSelectedKey(key)}
        />
      </Sider>
      <Layout>
        <Header style={{ 
          padding: '0 24px', 
          background: colorBgContainer,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <h1 style={{ margin: 0, color: '#1890ff' }}>
            🎯 福彩3D智能预测系统
          </h1>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Badge status="success" text="P9系统运行中" />
            <WebSocketStatus
              status={wsStatus}
              onReconnect={reconnectWS}
              showDetails={false}
            />
          </div>
        </Header>
        <Content style={{ margin: '24px 16px 0' }}>
          <div
            style={{
              padding: 24,
              minHeight: 360,
              background: colorBgContainer,
              borderRadius: borderRadiusLG,
            }}
          >
            {renderContent()}
          </div>
        </Content>
      </Layout>
      {/* 请求性能监控组件 */}
      <RequestMonitor />
      {/* 轮询优化监控组件 */}
      <PollingMonitor />
    </Layout>
  )
}

export default App
