#!/usr/bin/env python3
"""
修复配置加载器导入问题的脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def fix_config_imports():
    """修复配置导入问题"""
    
    # 需要修复的文件列表
    files_to_fix = [
        "src/predictors/hundreds_predictor.py",
        "src/predictors/tens_predictor.py", 
        "src/predictors/units_predictor.py",
        "src/predictors/span_predictor.py",
        "src/predictors/sum_predictor.py"
    ]
    
    for file_path in files_to_fix:
        full_path = project_root / file_path
        if not full_path.exists():
            print(f"⚠️ 文件不存在: {file_path}")
            continue
            
        try:
            # 读取文件内容
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否需要修复
            if "from config.config_loader import" in content and "sys.path.insert" not in content:
                print(f"🔧 修复文件: {file_path}")
                
                # 替换导入部分
                old_import = """# 导入配置加载器
try:
    from config.config_loader import get_config, setup_logging
except ImportError:
    print("警告: 无法导入配置加载器，将使用默认配置")"""

                new_import = """# 导入配置加载器
try:
    from config.config_loader import get_config, setup_logging
except ImportError:
    # 尝试添加项目根目录到路径
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(project_root))
    try:
        from config.config_loader import get_config, setup_logging
    except ImportError:
        print("警告: 无法导入配置加载器，将使用默认配置")
        get_config = None
        setup_logging = None"""
                
                if old_import in content:
                    content = content.replace(old_import, new_import)
                    
                    # 写回文件
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print(f"✅ 修复完成: {file_path}")
                else:
                    print(f"ℹ️ 无需修复: {file_path}")
            else:
                print(f"✅ 已修复或无需修复: {file_path}")
                
        except Exception as e:
            print(f"❌ 修复失败 {file_path}: {e}")

def test_config_import():
    """测试配置导入是否正常"""
    try:
        from config.config_loader import ConfigLoader, get_config
        loader = ConfigLoader()
        print("✅ 配置加载器导入测试成功")
        return True
    except Exception as e:
        print(f"❌ 配置加载器导入测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 配置导入修复脚本 ===")
    
    # 测试当前配置导入状态
    print("1. 测试配置导入...")
    if test_config_import():
        print("✅ 配置导入正常")
    else:
        print("⚠️ 配置导入有问题")
    
    # 修复配置导入
    print("\n2. 修复配置导入...")
    fix_config_imports()
    
    # 再次测试
    print("\n3. 再次测试配置导入...")
    if test_config_import():
        print("✅ 配置导入修复成功")
    else:
        print("❌ 配置导入修复失败")
    
    print("\n=== 修复完成 ===")

if __name__ == "__main__":
    main()
