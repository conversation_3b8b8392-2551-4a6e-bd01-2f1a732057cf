#!/usr/bin/env python3
"""
测试预测准确率监控系统
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.validation.accuracy_monitor import AccuracyMonitor

def test_accuracy_monitor():
    """测试准确率监控系统"""
    print("=== 测试预测准确率监控系统 ===")
    
    try:
        # 1. 初始化监控器
        print("1. 初始化准确率监控器...")
        monitor = AccuracyMonitor()
        print("✓ 准确率监控器初始化成功")
        
        # 2. 测试监控状态
        print("2. 检查监控系统状态...")
        status = monitor.get_monitoring_status()
        print(f"✓ 总记录数: {status['total_records']}")
        print(f"✓ 已验证记录: {status['verified_records']}")
        print(f"✓ 验证率: {status['verification_rate']:.1%}")
        print(f"✓ 最新记录: {status['latest_record']}")
        
        # 3. 测试记录预测结果
        print("3. 测试记录预测结果...")
        test_prediction = {
            'hundreds': 1,
            'tens': 2,
            'units': 3,
            'hundreds_confidence': 0.8,
            'tens_confidence': 0.7,
            'units_confidence': 0.9,
            'combined_probability': 0.75,
            'model_name': 'test_model',
            'fusion_method': 'adaptive_fusion'
        }
        
        success = monitor.record_prediction_result("2025999", test_prediction)
        print(f"✓ 记录预测结果: {'成功' if success else '失败'}")
        
        # 4. 测试验证预测结果
        print("4. 测试验证预测结果...")
        test_actual = {
            'hundreds': 1,  # 正确
            'tens': 5,      # 错误
            'units': 3      # 正确
        }
        
        success = monitor.verify_prediction_with_actual("2025999", test_actual)
        print(f"✓ 验证预测结果: {'成功' if success else '失败'}")
        
        # 5. 测试准确率计算
        print("5. 测试准确率指标计算...")
        metrics = monitor.calculate_accuracy_metrics(30)
        if metrics:
            print("✓ 准确率指标计算成功")
            overall = metrics.get('overall_stats', {})
            print(f"  总体准确率: {overall.get('overall_accuracy', 0):.1%}")
            print(f"  总预测数: {overall.get('total_predictions', 0)}")
            print(f"  平均置信度: {overall.get('avg_confidence', 0):.2f}")
        else:
            print("⚠ 准确率指标计算返回空结果（可能是数据不足）")
        
        # 6. 测试模型性能对比
        print("6. 测试模型性能对比...")
        comparison = monitor.get_model_performance_comparison(30)
        if comparison and 'models' in comparison:
            print("✓ 模型性能对比成功")
            print(f"  对比模型数: {len(comparison['models'])}")
            for model, stats in comparison['models'].items():
                print(f"  {model}: 准确率 {stats['accuracy']:.1%}, 预测数 {stats['total_predictions']}")
        else:
            print("⚠ 模型性能对比返回空结果（可能是数据不足）")
        
        # 7. 测试准确率趋势
        print("7. 测试准确率趋势分析...")
        trend = monitor.get_accuracy_trend(7)  # 7天趋势
        if trend and 'daily_accuracy' in trend:
            print("✓ 准确率趋势分析成功")
            print(f"  分析天数: {len(trend['daily_accuracy'])}")
        else:
            print("⚠ 准确率趋势分析返回空结果（可能是数据不足）")
        
        # 8. 测试历史回测
        print("8. 测试历史回测验证...")
        backtest = monitor.run_historical_backtest("2025200", "2025210")
        if 'error' not in backtest:
            print("✓ 历史回测验证成功")
            print(f"  回测期数: {backtest['total_issues']}")
            print(f"  组合准确率: {backtest['combination_accuracy']:.1%}")
            for position, accuracy in backtest['position_accuracy'].items():
                print(f"  {position}位准确率: {accuracy:.1%}")
        else:
            print(f"⚠ 历史回测验证失败: {backtest['error']}")
        
        # 9. 测试当前预测验证
        print("9. 测试当前预测质量验证...")
        validation = monitor.validate_current_predictions()
        if 'error' not in validation:
            print("✓ 当前预测质量验证成功")
            print(f"  验证预测数: {validation['predictions_count']}")
            conf_dist = validation['quality_checks']['confidence_distribution']
            print(f"  平均置信度: {conf_dist['mean']:.2f}")
            print(f"  高置信度比例: {conf_dist['high_confidence_ratio']:.1%}")
            print(f"  建议数量: {len(validation['recommendations'])}")
            for rec in validation['recommendations'][:3]:  # 显示前3个建议
                print(f"    - {rec}")
        else:
            print(f"⚠ 当前预测质量验证失败: {validation['error']}")
        
        # 10. 测试监控报告生成
        print("10. 测试监控报告生成...")
        report = monitor.generate_monitoring_report(30)
        if report and 'summary' in report:
            print("✓ 监控报告生成成功")
            summary = report['summary']
            print(f"  报告期间: {report['period']}")
            print(f"  总体准确率: {summary.get('overall_accuracy', 0):.1%}")
            print(f"  最佳位置: {summary.get('best_position', 'N/A')}")
            print(f"  组合准确率: {summary.get('combination_accuracy', 0):.1%}")
        else:
            print("⚠ 监控报告生成返回空结果（可能是数据不足）")
        
        print("\n=== 准确率监控系统测试完成 ===")
        print("✓ 所有核心功能测试通过")
        print("📊 监控系统已准备就绪，可开始实时监控预测质量")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_accuracy_monitor()
    sys.exit(0 if success else 1)
