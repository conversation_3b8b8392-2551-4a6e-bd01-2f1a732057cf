# WebSocket连接管理器
# 为P10-Web界面系统提供实时数据推送功能

from fastapi import WebSocket
from typing import List
import asyncio
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self, api_adapter):
        self.connections: List[WebSocket] = []
        self.api_adapter = api_adapter
        self.is_running = False
        logger.info("WebSocket管理器初始化完成")

    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.connections.append(websocket)
        logger.info(f"WebSocket连接已建立，当前连接数: {len(self.connections)}")
        
        # 发送欢迎消息
        await self.send_to_websocket(websocket, {
            'type': 'connection_established',
            'message': '连接已建立',
            'timestamp': datetime.now().isoformat()
        })

    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.connections:
            self.connections.remove(websocket)
            logger.info(f"WebSocket连接已断开，当前连接数: {len(self.connections)}")

    async def send_to_websocket(self, websocket: WebSocket, message: dict):
        """发送消息到指定WebSocket连接"""
        try:
            # 检查连接状态
            if hasattr(websocket, 'client_state') and websocket.client_state.name != 'CONNECTED':
                self.disconnect(websocket)
                return False

            message_str = json.dumps(message, ensure_ascii=False)
            await websocket.send_text(message_str)
            return True
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            self.disconnect(websocket)
            return False

    async def broadcast(self, message: dict):
        """广播消息到所有连接"""
        if not self.connections:
            return

        message_str = json.dumps(message, ensure_ascii=False)
        disconnected = []

        for connection in self.connections:
            try:
                # 检查连接状态
                if hasattr(connection, 'client_state') and connection.client_state.name != 'CONNECTED':
                    disconnected.append(connection)
                    continue

                await connection.send_text(message_str)
            except Exception as e:
                logger.warning(f"广播消息失败，连接将被移除: {e}")
                disconnected.append(connection)

        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)

        if disconnected:
            logger.info(f"清理了 {len(disconnected)} 个断开的连接")

    async def start_background_tasks(self):
        """启动后台任务"""
        self.is_running = True
        logger.info("启动WebSocket后台任务")

        # 启动状态更新任务
        asyncio.create_task(self._status_update_task())

        # 启动性能监控任务
        asyncio.create_task(self._performance_monitor_task())

        # 启动心跳任务
        asyncio.create_task(self._heartbeat_task())

    async def _status_update_task(self):
        """状态更新任务 - 每30秒推送一次系统状态"""
        while self.is_running:
            try:
                if self.connections:
                    # 获取系统状态数据
                    dashboard_data = await self.api_adapter.get_dashboard_data()

                    # 获取最新预测数据
                    latest_predictions = await self.api_adapter._get_latest_predictions()

                    # 组合数据
                    combined_data = {
                        'system_status': dashboard_data.get('system_status', {}),
                        'predictions': latest_predictions[:5],  # 只推送前5个预测
                        'performance_metrics': dashboard_data.get('performance_metrics', {}),
                        'optimization_tasks': dashboard_data.get('optimization_tasks', [])
                    }

                    await self.broadcast({
                        'type': 'status_update',
                        'data': combined_data,
                        'timestamp': datetime.now().isoformat()
                    })
                    logger.debug("系统状态更新已推送")
            except Exception as e:
                logger.error(f"状态更新失败: {e}")

            await asyncio.sleep(30)  # 每30秒更新

    async def _performance_monitor_task(self):
        """性能监控任务 - 每分钟推送一次性能数据"""
        while self.is_running:
            try:
                if self.connections:
                    performance_data = await self.api_adapter._get_performance_metrics()
                    await self.broadcast({
                        'type': 'performance_update',
                        'data': performance_data,
                        'timestamp': datetime.now().isoformat()
                    })
                    logger.debug("性能监控数据已推送")
            except Exception as e:
                logger.error(f"性能监控更新失败: {e}")

            await asyncio.sleep(60)  # 每分钟更新

    async def _heartbeat_task(self):
        """心跳任务 - 每10秒发送心跳保持连接活跃"""
        while self.is_running:
            try:
                if self.connections:
                    await self.broadcast({
                        'type': 'heartbeat',
                        'timestamp': datetime.now().isoformat(),
                        'connections': len(self.connections)
                    })
                    logger.debug(f"心跳已发送，活跃连接数: {len(self.connections)}")
            except Exception as e:
                logger.error(f"心跳发送失败: {e}")

            await asyncio.sleep(10)  # 每10秒心跳

    async def notify_prediction_update(self, prediction_data: dict):
        """通知预测结果更新"""
        await self.broadcast({
            'type': 'prediction_updated',
            'data': prediction_data,
            'timestamp': datetime.now().isoformat()
        })
        logger.info("预测结果更新通知已发送")

    async def notify_optimization_status(self, status_data: dict):
        """通知优化状态变化"""
        await self.broadcast({
            'type': 'optimization_status_changed',
            'data': status_data,
            'timestamp': datetime.now().isoformat()
        })
        logger.info("优化状态变化通知已发送")

    async def notify_system_alert(self, alert_data: dict):
        """发送系统警报"""
        await self.broadcast({
            'type': 'system_alert',
            'data': alert_data,
            'timestamp': datetime.now().isoformat(),
            'priority': alert_data.get('priority', 'normal')
        })
        logger.warning(f"系统警报已发送: {alert_data.get('message', 'Unknown alert')}")

    async def notify_new_prediction(self, prediction_data: dict):
        """通知新的预测结果"""
        await self.broadcast({
            'type': 'new_prediction',
            'data': prediction_data,
            'timestamp': datetime.now().isoformat()
        })
        logger.info("新预测结果通知已发送")

    async def notify_task_progress(self, task_id: str, progress: int, status: str):
        """通知任务进度更新"""
        await self.broadcast({
            'type': 'task_progress',
            'data': {
                'task_id': task_id,
                'progress': progress,
                'status': status
            },
            'timestamp': datetime.now().isoformat()
        })
        logger.info(f"任务进度更新通知已发送: {task_id} - {progress}%")

    async def notify_resource_warning(self, resource_type: str, usage: float, threshold: float):
        """通知资源使用警告"""
        await self.broadcast({
            'type': 'resource_warning',
            'data': {
                'resource_type': resource_type,
                'usage': usage,
                'threshold': threshold,
                'severity': 'warning' if usage > threshold else 'critical'
            },
            'timestamp': datetime.now().isoformat()
        })
        logger.warning(f"资源警告已发送: {resource_type} 使用率 {usage}%")

    def get_connection_stats(self) -> dict:
        """获取连接统计信息"""
        return {
            'total_connections': len(self.connections),
            'is_running': self.is_running,
            'last_update': datetime.now().isoformat()
        }

    async def stop(self):
        """停止WebSocket管理器"""
        self.is_running = False
        
        # 通知所有连接即将关闭
        await self.broadcast({
            'type': 'server_shutdown',
            'message': '服务器即将关闭',
            'timestamp': datetime.now().isoformat()
        })
        
        # 关闭所有连接
        for connection in self.connections.copy():
            try:
                await connection.close()
            except:
                pass
        
        self.connections.clear()
        logger.info("WebSocket管理器已停止")
