"""
P2特征工程接口
为预测器提供统一的特征获取接口，连接P2特征工程系统
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from data.predictor_features import (
    generate_hundreds_features,
    generate_tens_features,
    generate_units_features,
    generate_common_features
)
import sqlite3

logger = logging.getLogger(__name__)


class PredictorFeatureInterface:
    """
    预测器特征接口
    
    为不同位置的预测器提供统一的特征获取接口，
    连接P2特征工程系统，确保特征数据的一致性和准确性。
    """
    
    def __init__(self, position: str, window_sizes: List[int] = None):
        """
        初始化特征接口
        
        Args:
            position: 预测位置 ('hundreds', 'tens', 'units')
            window_sizes: 滑动窗口大小列表
        """
        self.position = position
        self.window_sizes = window_sizes or [5, 10, 20, 50]
        self.db_path = "data/fucai3d.db"  # 默认数据库路径
        self.feature_cache = {}
        
        # 验证位置参数
        valid_positions = ['hundreds', 'tens', 'units']
        if position not in valid_positions:
            raise ValueError(f"位置参数必须是 {valid_positions} 之一")
        
        logger.info(f"初始化{position}位特征接口，窗口大小: {self.window_sizes}")
    
    def get_prediction_features(self, issue: str, feature_count: int = 50) -> List[float]:
        """
        获取指定期号的预测特征
        
        Args:
            issue: 期号
            feature_count: 需要的特征数量
            
        Returns:
            List[float]: 特征向量
        """
        try:
            # 检查缓存
            cache_key = f"{self.position}_{issue}_{feature_count}"
            if cache_key in self.feature_cache:
                logger.debug(f"使用缓存特征: {cache_key}")
                return self.feature_cache[cache_key]
            
            # 获取历史数据
            historical_data = self._get_historical_data(issue)
            if historical_data.empty:
                logger.warning(f"无法获取期号 {issue} 的历史数据，返回零特征")
                return [0.0] * feature_count
            
            # 生成特征
            features_df = self._generate_features(historical_data)
            
            # 提取最新的特征向量
            feature_vector = self._extract_feature_vector(features_df, feature_count)
            
            # 缓存结果
            self.feature_cache[cache_key] = feature_vector
            
            logger.info(f"成功生成{self.position}位特征，期号: {issue}, 特征数: {len(feature_vector)}")
            return feature_vector
            
        except Exception as e:
            logger.error(f"获取预测特征失败: {e}")
            # 返回零特征作为fallback
            return [0.0] * feature_count
    
    def _get_historical_data(self, issue: str, lookback_periods: int = 200) -> pd.DataFrame:
        """获取历史数据"""
        try:
            # 连接数据库
            conn = sqlite3.connect(self.db_path)

            # 获取指定期号之前的历史数据
            query = """
            SELECT issue, hundreds, tens, units, sum_value, span_value, created_at as draw_date
            FROM final_predictions
            WHERE issue < ? AND prediction_rank = 1
            ORDER BY issue DESC
            LIMIT ?
            """

            df = pd.read_sql_query(query, conn, params=(issue, lookback_periods))
            conn.close()

            if df.empty:
                logger.warning(f"未找到期号 {issue} 之前的历史数据")
                return pd.DataFrame()

            # 按期号正序排列（时间顺序）
            df = df.sort_values('issue').reset_index(drop=True)

            logger.debug(f"获取历史数据成功，数据量: {len(df)}")
            return df

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def _generate_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成特征"""
        try:
            # 根据位置生成对应的特征
            if self.position == 'hundreds':
                features_df = generate_hundreds_features(df, self.window_sizes)
            elif self.position == 'tens':
                features_df = generate_tens_features(df, self.window_sizes)
            elif self.position == 'units':
                features_df = generate_units_features(df, self.window_sizes)
            else:
                raise ValueError(f"不支持的位置: {self.position}")
            
            # 添加通用特征
            common_features = generate_common_features(df, self.window_sizes)
            
            # 合并特征
            for col in common_features.columns:
                if col not in features_df.columns:
                    features_df[col] = common_features[col]
            
            logger.debug(f"生成特征成功，特征维度: {features_df.shape}")
            return features_df
            
        except Exception as e:
            logger.error(f"生成特征失败: {e}")
            raise
    
    def _extract_feature_vector(self, features_df: pd.DataFrame, feature_count: int) -> List[float]:
        """提取特征向量"""
        try:
            # 获取最后一行的特征（最新特征）
            if features_df.empty:
                return [0.0] * feature_count
            
            latest_features = features_df.iloc[-1]
            
            # 移除非数值列
            if hasattr(latest_features, 'select_dtypes'):
                numeric_features = latest_features.select_dtypes(include=[np.number])
            else:
                # 如果是Series，直接使用
                numeric_features = latest_features
            
            # 处理NaN值
            numeric_features = numeric_features.fillna(0.0)
            
            # 转换为列表
            feature_list = numeric_features.tolist()
            
            # 调整特征数量
            if len(feature_list) >= feature_count:
                # 如果特征过多，选择前N个
                feature_vector = feature_list[:feature_count]
            else:
                # 如果特征不足，用0填充
                feature_vector = feature_list + [0.0] * (feature_count - len(feature_list))
            
            logger.debug(f"提取特征向量成功，长度: {len(feature_vector)}")
            return feature_vector
            
        except Exception as e:
            logger.error(f"提取特征向量失败: {e}")
            return [0.0] * feature_count
    
    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        try:
            # 使用示例数据生成特征名称
            sample_data = pd.DataFrame({
                'issue': ['2025001', '2025002'],
                'hundreds': [1, 2],
                'tens': [2, 3],
                'units': [3, 4],
                'sum_value': [6, 9],
                'span_value': [2, 2]
            })
            
            features_df = self._generate_features(sample_data)
            numeric_columns = features_df.select_dtypes(include=[np.number]).columns.tolist()
            
            logger.debug(f"获取特征名称成功，数量: {len(numeric_columns)}")
            return numeric_columns
            
        except Exception as e:
            logger.error(f"获取特征名称失败: {e}")
            return []
    
    def clear_cache(self):
        """清空特征缓存"""
        self.feature_cache.clear()
        logger.info("特征缓存已清空")
