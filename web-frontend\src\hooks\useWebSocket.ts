import { useState, useEffect, useRef, useCallback } from 'react'
import { message } from 'antd'

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
  priority?: string
}

export interface WebSocketStatus {
  connected: boolean
  connecting: boolean
  error: string | null
  lastMessage: WebSocketMessage | null
  connectionCount: number
}

export interface UseWebSocketOptions {
  url?: string
  autoReconnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
  onMessage?: (message: WebSocketMessage) => void
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Event) => void
}

export const useWebSocket = (options: UseWebSocketOptions = {}) => {
  const {
    url = 'ws://127.0.0.1:8000/ws',
    autoReconnect = true,
    reconnectInterval = 3000,
    maxReconnectAttempts = 10,
    onMessage,
    onConnect,
    onDisconnect,
    onError
  } = options

  const [status, setStatus] = useState<WebSocketStatus>({
    connected: false,
    connecting: false,
    error: null,
    lastMessage: null,
    connectionCount: 0
  })

  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectAttemptsRef = useRef(0)
  const mountedRef = useRef(true)

  // 清理重连定时器
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
  }, [])

  // 连接WebSocket
  const connect = useCallback(() => {
    if (!mountedRef.current) return

    // 如果已经连接或正在连接，不重复连接
    if (wsRef.current?.readyState === WebSocket.CONNECTING || 
        wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    setStatus(prev => ({ ...prev, connecting: true, error: null }))

    try {
      const ws = new WebSocket(url)
      wsRef.current = ws

      ws.onopen = () => {
        if (!mountedRef.current) return
        
        console.log('WebSocket连接已建立')
        setStatus(prev => ({
          ...prev,
          connected: true,
          connecting: false,
          error: null,
          connectionCount: prev.connectionCount + 1
        }))
        
        reconnectAttemptsRef.current = 0
        onConnect?.()
      }

      ws.onmessage = (event) => {
        if (!mountedRef.current) return

        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          
          setStatus(prev => ({
            ...prev,
            lastMessage: message
          }))

          // 处理不同类型的消息
          handleMessage(message)
          onMessage?.(message)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }

      ws.onclose = (event) => {
        if (!mountedRef.current) return

        console.log('WebSocket连接已关闭:', event.code, event.reason)
        setStatus(prev => ({
          ...prev,
          connected: false,
          connecting: false
        }))

        wsRef.current = null
        onDisconnect?.()

        // 只有在非正常关闭时才自动重连
        if (autoReconnect && event.code !== 1000 && reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++
          console.log(`WebSocket异常关闭，尝试重连 (${reconnectAttemptsRef.current}/${maxReconnectAttempts})`)

          reconnectTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              connect()
            }
          }, reconnectInterval)
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          setStatus(prev => ({
            ...prev,
            error: '重连次数已达上限'
          }))
          console.warn('WebSocket重连次数已达上限，停止重连')
        } else if (event.code === 1000) {
          console.log('WebSocket正常关闭')
        }
      }

      ws.onerror = (error) => {
        if (!mountedRef.current) return

        console.warn('WebSocket连接错误:', error)
        setStatus(prev => ({
          ...prev,
          error: 'WebSocket连接错误',
          connecting: false
        }))

        onError?.(error)
      }

    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      setStatus(prev => ({
        ...prev,
        error: '创建连接失败',
        connecting: false
      }))
    }
  }, [url, autoReconnect, reconnectInterval, maxReconnectAttempts, onConnect, onDisconnect, onError, onMessage])

  // 处理WebSocket消息
  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'system_alert':
        if (message.data.priority === 'critical') {
          message.error(message.data.message || '系统严重警报')
        } else {
          message.warning(message.data.message || '系统警告')
        }
        break

      case 'resource_warning':
        const { resource_type, usage, threshold } = message.data
        message.warning(`${resource_type}使用率过高: ${usage}% (阈值: ${threshold}%)`)
        break

      case 'new_prediction':
        message.success('新的预测结果已生成')
        break

      case 'task_progress':
        // 任务进度更新通常不需要弹窗提示
        console.log('任务进度更新:', message.data)
        break

      case 'heartbeat':
        // 心跳消息，不需要处理
        break

      default:
        console.log('收到WebSocket消息:', message)
    }
  }, [])

  // 断开连接
  const disconnect = useCallback(() => {
    clearReconnectTimeout()
    reconnectAttemptsRef.current = maxReconnectAttempts // 阻止自动重连
    
    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }
    
    setStatus(prev => ({
      ...prev,
      connected: false,
      connecting: false
    }))
  }, [clearReconnectTimeout, maxReconnectAttempts])

  // 发送消息
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
      return true
    }
    console.warn('WebSocket未连接，无法发送消息')
    return false
  }, [])

  // 手动重连
  const reconnect = useCallback(() => {
    disconnect()
    reconnectAttemptsRef.current = 0
    setTimeout(connect, 1000)
  }, [disconnect, connect])

  // 发送心跳
  const sendHeartbeat = useCallback(() => {
    return sendMessage('ping')
  }, [sendMessage])

  // 初始化连接
  useEffect(() => {
    connect()

    return () => {
      mountedRef.current = false
      clearReconnectTimeout()
      if (wsRef.current) {
        wsRef.current.close()
      }
    }
  }, [connect, clearReconnectTimeout])

  // 定期发送心跳
  useEffect(() => {
    if (!status.connected) return

    const heartbeatInterval = setInterval(() => {
      sendHeartbeat()
    }, 30000) // 每30秒发送一次心跳

    return () => clearInterval(heartbeatInterval)
  }, [status.connected, sendHeartbeat])

  return {
    status,
    connect,
    disconnect,
    reconnect,
    sendMessage,
    sendHeartbeat
  }
}
