#!/usr/bin/env python3
"""
SHAP可视化组件
为Web界面提供SHAP特征重要性和预测解释的可视化功能
"""

import sys
from pathlib import Path
import json
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.fusion.prediction_explainer import PredictionExplainer
except ImportError as e:
    print(f"警告: 无法导入预测解释器: {e}")

logger = logging.getLogger(__name__)


class SHAPVisualizationComponent:
    """
    SHAP可视化组件
    
    为Web界面提供SHAP解释结果的可视化和格式化功能，
    将复杂的SHAP分析结果转换为用户友好的界面元素。
    """
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        """
        初始化SHAP可视化组件
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.explainer = PredictionExplainer(db_path)
        
        logger.info("SHAP可视化组件初始化完成")
    
    def format_prediction_explanation(self, explanation: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化预测解释为Web界面友好的格式
        
        Args:
            explanation: 预测解释结果
            
        Returns:
            Dict: 格式化后的解释数据
        """
        try:
            if not explanation or 'predicted_number' not in explanation:
                return self._get_empty_explanation()
            
            # 基础信息
            formatted = {
                'prediction_info': {
                    'number': explanation['predicted_number'],
                    'position': explanation['position'],
                    'confidence': explanation['confidence'],
                    'confidence_level': self._get_confidence_level(explanation['confidence']),
                    'confidence_color': self._get_confidence_color(explanation['confidence'])
                },
                'overall_explanation': explanation.get('overall_explanation', ''),
                'explanation_type': explanation.get('explanation_type', 'unknown')
            }
            
            # 特征解释
            feature_explanations = explanation.get('feature_explanations', [])
            formatted['features'] = []
            
            for feat in feature_explanations:
                formatted_feature = {
                    'name': feat.get('feature_name', ''),
                    'description': feat.get('description', ''),
                    'support_type': feat.get('support_type', ''),
                    'strength': feat.get('strength', ''),
                    'shap_value': feat.get('shap_value', 0),
                    'feature_value': feat.get('feature_value', 0),
                    'impact_color': self._get_impact_color(feat.get('shap_value', 0)),
                    'strength_icon': self._get_strength_icon(feat.get('strength', '')),
                    'support_icon': self._get_support_icon(feat.get('support_type', ''))
                }
                formatted['features'].append(formatted_feature)
            
            # SHAP摘要
            shap_summary = explanation.get('shap_summary')
            if shap_summary:
                formatted['shap_summary'] = {
                    'total_positive': shap_summary.get('total_positive_impact', 0),
                    'total_negative': shap_summary.get('total_negative_impact', 0),
                    'net_support': shap_summary.get('net_support', 0),
                    'support_ratio': self._calculate_support_ratio(shap_summary)
                }
            
            return formatted
            
        except Exception as e:
            logger.error(f"格式化预测解释失败: {e}")
            return self._get_empty_explanation()
    
    def format_fusion_explanation(self, fusion_explanation: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化融合解释为Web界面友好的格式
        
        Args:
            fusion_explanation: 融合解释结果
            
        Returns:
            Dict: 格式化后的融合解释
        """
        try:
            if 'error' in fusion_explanation:
                return {'error': fusion_explanation['error']}
            
            formatted = {
                'fusion_info': {
                    'method': fusion_explanation.get('fusion_method', 'unknown'),
                    'total_predictions': fusion_explanation.get('total_predictions', 0),
                    'summary': fusion_explanation.get('summary', '')
                },
                'top_predictions': []
            }
            
            # 格式化预测结果
            explained_predictions = fusion_explanation.get('explained_predictions', [])
            for pred in explained_predictions:
                formatted_pred = {
                    'rank': pred.get('rank', 0),
                    'combination': pred.get('combination', ''),
                    'probability': pred.get('probability', 0),
                    'probability_percent': f"{pred.get('probability', 0):.1%}",
                    'explanation': pred.get('explanation', ''),
                    'confidence_color': self._get_confidence_color(pred.get('probability', 0)),
                    'rank_badge': self._get_rank_badge(pred.get('rank', 0))
                }
                formatted['top_predictions'].append(formatted_pred)
            
            return formatted
            
        except Exception as e:
            logger.error(f"格式化融合解释失败: {e}")
            return {'error': f'格式化失败: {e}'}
    
    def generate_explanation_html(self, explanation: Dict[str, Any]) -> str:
        """
        生成解释的HTML代码
        
        Args:
            explanation: 格式化后的解释数据
            
        Returns:
            str: HTML代码
        """
        try:
            if 'error' in explanation:
                return f'<div class="alert alert-warning">{explanation["error"]}</div>'
            
            html_parts = []
            
            # 预测信息
            pred_info = explanation.get('prediction_info', {})
            html_parts.append(f'''
            <div class="prediction-header">
                <h4>预测结果</h4>
                <div class="prediction-number">
                    <span class="number">{pred_info.get('number', 'N/A')}</span>
                    <span class="position">({pred_info.get('position', '')}位)</span>
                </div>
                <div class="confidence-badge {pred_info.get('confidence_color', '')}">
                    置信度: {pred_info.get('confidence', 0):.1%} ({pred_info.get('confidence_level', '')})
                </div>
            </div>
            ''')
            
            # 总体解释
            overall = explanation.get('overall_explanation', '')
            if overall:
                html_parts.append(f'''
                <div class="overall-explanation">
                    <h5>AI分析依据</h5>
                    <p>{overall}</p>
                </div>
                ''')
            
            # 特征解释
            features = explanation.get('features', [])
            if features:
                html_parts.append('<div class="feature-explanations"><h5>关键特征分析</h5>')
                
                for feat in features:
                    html_parts.append(f'''
                    <div class="feature-item">
                        <div class="feature-header">
                            <span class="support-icon">{feat.get('support_icon', '')}</span>
                            <span class="feature-name">{feat.get('name', '')}</span>
                            <span class="strength-badge {feat.get('impact_color', '')}">{feat.get('strength', '')}</span>
                        </div>
                        <div class="feature-description">{feat.get('description', '')}</div>
                    </div>
                    ''')
                
                html_parts.append('</div>')
            
            # SHAP摘要
            shap_summary = explanation.get('shap_summary')
            if shap_summary:
                html_parts.append(f'''
                <div class="shap-summary">
                    <h5>特征影响分析</h5>
                    <div class="impact-bars">
                        <div class="positive-impact">
                            <span>支持因素: {shap_summary.get('total_positive', 0):.3f}</span>
                        </div>
                        <div class="negative-impact">
                            <span>反对因素: {shap_summary.get('total_negative', 0):.3f}</span>
                        </div>
                        <div class="net-impact">
                            <span>综合影响: {shap_summary.get('net_support', 0):.3f}</span>
                        </div>
                    </div>
                </div>
                ''')
            
            return ''.join(html_parts)
            
        except Exception as e:
            logger.error(f"生成HTML失败: {e}")
            return f'<div class="alert alert-danger">生成解释界面失败: {e}</div>'
    
    def generate_fusion_html(self, fusion_explanation: Dict[str, Any]) -> str:
        """
        生成融合解释的HTML代码
        
        Args:
            fusion_explanation: 格式化后的融合解释
            
        Returns:
            str: HTML代码
        """
        try:
            if 'error' in fusion_explanation:
                return f'<div class="alert alert-warning">{fusion_explanation["error"]}</div>'
            
            html_parts = []
            
            # 融合信息
            fusion_info = fusion_explanation.get('fusion_info', {})
            html_parts.append(f'''
            <div class="fusion-header">
                <h4>智能融合预测</h4>
                <div class="fusion-method">
                    <span>融合方法: {fusion_info.get('method', 'unknown')}</span>
                    <span>分析组合: {fusion_info.get('total_predictions', 0)} 个</span>
                </div>
                <p class="fusion-summary">{fusion_info.get('summary', '')}</p>
            </div>
            ''')
            
            # 推荐预测
            top_predictions = fusion_explanation.get('top_predictions', [])
            if top_predictions:
                html_parts.append('<div class="top-predictions"><h5>推荐组合</h5>')
                
                for pred in top_predictions:
                    html_parts.append(f'''
                    <div class="prediction-item">
                        <div class="rank-badge {pred.get('rank_badge', '')}">{pred.get('rank', 0)}</div>
                        <div class="combination">{pred.get('combination', '')}</div>
                        <div class="probability {pred.get('confidence_color', '')}">{pred.get('probability_percent', '')}</div>
                        <div class="explanation">{pred.get('explanation', '')}</div>
                    </div>
                    ''')
                
                html_parts.append('</div>')
            
            return ''.join(html_parts)
            
        except Exception as e:
            logger.error(f"生成融合HTML失败: {e}")
            return f'<div class="alert alert-danger">生成融合界面失败: {e}</div>'
    
    def _get_confidence_level(self, confidence: float) -> str:
        """获取置信度等级"""
        if confidence >= 0.7:
            return "高"
        elif confidence >= 0.4:
            return "中"
        else:
            return "低"
    
    def _get_confidence_color(self, confidence: float) -> str:
        """获取置信度颜色类"""
        if confidence >= 0.7:
            return "confidence-high"
        elif confidence >= 0.4:
            return "confidence-medium"
        else:
            return "confidence-low"
    
    def _get_impact_color(self, shap_value: float) -> str:
        """获取影响颜色类"""
        if shap_value > 0.05:
            return "impact-strong-positive"
        elif shap_value > 0:
            return "impact-positive"
        elif shap_value < -0.05:
            return "impact-strong-negative"
        else:
            return "impact-negative"
    
    def _get_strength_icon(self, strength: str) -> str:
        """获取强度图标"""
        icons = {
            '强烈': '🔥',
            '中等': '⚡',
            '轻微': '💫'
        }
        return icons.get(strength, '📊')
    
    def _get_support_icon(self, support_type: str) -> str:
        """获取支持类型图标"""
        icons = {
            '支持': '✅',
            '反对': '⚠️'
        }
        return icons.get(support_type, '📈')
    
    def _get_rank_badge(self, rank: int) -> str:
        """获取排名徽章类"""
        if rank == 1:
            return "rank-first"
        elif rank == 2:
            return "rank-second"
        elif rank == 3:
            return "rank-third"
        else:
            return "rank-other"
    
    def _calculate_support_ratio(self, shap_summary: Dict[str, Any]) -> float:
        """计算支持比例"""
        positive = abs(shap_summary.get('total_positive_impact', 0))
        negative = abs(shap_summary.get('total_negative_impact', 0))
        total = positive + negative
        
        if total == 0:
            return 0.5
        
        return positive / total
    
    def _get_empty_explanation(self) -> Dict[str, Any]:
        """获取空解释结构"""
        return {
            'prediction_info': {
                'number': 'N/A',
                'position': '',
                'confidence': 0,
                'confidence_level': '未知',
                'confidence_color': 'confidence-unknown'
            },
            'overall_explanation': '暂无解释信息',
            'features': [],
            'shap_summary': None,
            'explanation_type': 'empty'
        }
    
    def get_css_styles(self) -> str:
        """获取SHAP可视化的CSS样式"""
        return '''
        <style>
        .prediction-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .prediction-number {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }
        
        .confidence-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .confidence-high { background-color: #28a745; }
        .confidence-medium { background-color: #ffc107; color: #212529; }
        .confidence-low { background-color: #dc3545; }
        
        .feature-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        
        .feature-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        
        .strength-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .impact-strong-positive { background-color: #28a745; color: white; }
        .impact-positive { background-color: #d4edda; color: #155724; }
        .impact-strong-negative { background-color: #dc3545; color: white; }
        .impact-negative { background-color: #f8d7da; color: #721c24; }
        
        .prediction-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin: 10px 0;
            background: white;
        }
        
        .rank-badge {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        
        .rank-first { background-color: #ffd700; color: #333; }
        .rank-second { background-color: #c0c0c0; color: #333; }
        .rank-third { background-color: #cd7f32; }
        .rank-other { background-color: #6c757d; }
        
        .combination {
            font-size: 1.5em;
            font-weight: bold;
            font-family: monospace;
        }
        
        .shap-summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .impact-bars {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .impact-bars > div {
            padding: 8px;
            border-radius: 4px;
        }
        
        .positive-impact { background-color: #d4edda; color: #155724; }
        .negative-impact { background-color: #f8d7da; color: #721c24; }
        .net-impact { background-color: #d1ecf1; color: #0c5460; }
        </style>
        '''
