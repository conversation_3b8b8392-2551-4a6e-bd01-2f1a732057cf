"""
SHAP解释API路由
提供预测解释和特征重要性分析的Web接口
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.fusion.prediction_explainer import PredictionExplainer
except ImportError as e:
    logging.warning(f"无法导入SHAP解释器: {e}")
    PredictionExplainer = None

# 创建路由器
router = APIRouter(prefix="/api/shap", tags=["SHAP解释"])

# 全局SHAP解释器实例
_explainer = None

def get_explainer():
    """获取SHAP解释器实例"""
    global _explainer
    if _explainer is None and PredictionExplainer is not None:
        try:
            _explainer = PredictionExplainer()
            logging.info("SHAP解释器初始化成功")
        except Exception as e:
            logging.error(f"SHAP解释器初始化失败: {e}")
            _explainer = None
    return _explainer

@router.get("/status", summary="获取SHAP解释器状态")
async def get_shap_status():
    """
    获取SHAP解释器状态
    
    Returns:
        Dict: SHAP解释器状态信息
    """
    try:
        explainer = get_explainer()
        if explainer is None:
            return {
                "status": "unavailable",
                "message": "SHAP解释器不可用",
                "shap_available": False,
                "timestamp": datetime.now().isoformat()
            }
        
        status = explainer.get_explainer_status()
        status["status"] = "available"
        status["message"] = "SHAP解释器正常运行"
        status["timestamp"] = datetime.now().isoformat()
        
        return status
        
    except Exception as e:
        logging.error(f"获取SHAP状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取SHAP状态失败: {str(e)}")

@router.post("/explain/prediction", summary="解释单个预测结果")
async def explain_prediction(
    prediction_number: str = Query(..., description="预测号码，如'123'"),
    position: str = Query(..., description="位置：hundreds/tens/units"),
    model_type: str = Query(default="xgboost", description="模型类型")
):
    """
    解释单个预测结果
    
    Args:
        prediction_number: 预测号码
        position: 位置（百位/十位/个位）
        model_type: 模型类型
        
    Returns:
        Dict: 预测解释结果
    """
    try:
        explainer = get_explainer()
        if explainer is None:
            raise HTTPException(status_code=503, detail="SHAP解释器不可用")
        
        # 验证参数
        if position not in ["hundreds", "tens", "units"]:
            raise HTTPException(status_code=400, detail="位置参数必须是 hundreds, tens, 或 units")
        
        if len(prediction_number) != 3 or not prediction_number.isdigit():
            raise HTTPException(status_code=400, detail="预测号码必须是3位数字")
        
        # 获取预测解释
        explanation = explainer.explain_prediction_api(
            prediction_number=prediction_number,
            position=position,
            model_type=model_type
        )
        
        return {
            "status": "success",
            "prediction_number": prediction_number,
            "position": position,
            "model_type": model_type,
            "explanation": explanation,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"解释预测失败: {e}")
        raise HTTPException(status_code=500, detail=f"解释预测失败: {str(e)}")

@router.get("/explain/features/{position}", summary="获取位置特征重要性")
async def get_feature_importance(
    position: str,
    model_type: str = Query(default="xgboost", description="模型类型"),
    top_n: int = Query(default=10, description="返回前N个重要特征")
):
    """
    获取指定位置的特征重要性
    
    Args:
        position: 位置（百位/十位/个位）
        model_type: 模型类型
        top_n: 返回前N个重要特征
        
    Returns:
        Dict: 特征重要性分析结果
    """
    try:
        explainer = get_explainer()
        if explainer is None:
            raise HTTPException(status_code=503, detail="SHAP解释器不可用")
        
        # 验证参数
        if position not in ["hundreds", "tens", "units"]:
            raise HTTPException(status_code=400, detail="位置参数必须是 hundreds, tens, 或 units")
        
        # 获取特征重要性
        feature_importance = explainer.get_feature_importance(
            position=position,
            model_type=model_type,
            top_n=top_n
        )
        
        return {
            "status": "success",
            "position": position,
            "model_type": model_type,
            "top_n": top_n,
            "feature_importance": feature_importance,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"获取特征重要性失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取特征重要性失败: {str(e)}")

@router.post("/explain/batch", summary="批量解释预测结果")
async def explain_batch_predictions(
    predictions: List[Dict[str, str]],
    position: str = Query(..., description="位置：hundreds/tens/units"),
    model_type: str = Query(default="xgboost", description="模型类型")
):
    """
    批量解释预测结果
    
    Args:
        predictions: 预测列表，每个包含prediction_number
        position: 位置
        model_type: 模型类型
        
    Returns:
        Dict: 批量解释结果
    """
    try:
        explainer = get_explainer()
        if explainer is None:
            raise HTTPException(status_code=503, detail="SHAP解释器不可用")
        
        # 验证参数
        if position not in ["hundreds", "tens", "units"]:
            raise HTTPException(status_code=400, detail="位置参数必须是 hundreds, tens, 或 units")
        
        if len(predictions) > 20:
            raise HTTPException(status_code=400, detail="批量解释最多支持20个预测")
        
        # 批量解释
        results = []
        for pred in predictions:
            try:
                prediction_number = pred.get("prediction_number", "")
                if len(prediction_number) != 3 or not prediction_number.isdigit():
                    results.append({
                        "prediction_number": prediction_number,
                        "status": "error",
                        "error": "预测号码必须是3位数字"
                    })
                    continue
                
                explanation = explainer.explain_prediction_api(
                    prediction_number=prediction_number,
                    position=position,
                    model_type=model_type
                )
                
                results.append({
                    "prediction_number": prediction_number,
                    "status": "success",
                    "explanation": explanation
                })
                
            except Exception as e:
                results.append({
                    "prediction_number": pred.get("prediction_number", ""),
                    "status": "error",
                    "error": str(e)
                })
        
        return {
            "status": "success",
            "position": position,
            "model_type": model_type,
            "total_predictions": len(predictions),
            "successful_explanations": len([r for r in results if r["status"] == "success"]),
            "results": results,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"批量解释失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量解释失败: {str(e)}")

@router.get("/explain/summary/{position}", summary="获取位置预测解释摘要")
async def get_explanation_summary(
    position: str,
    model_type: str = Query(default="xgboost", description="模型类型")
):
    """
    获取指定位置的预测解释摘要
    
    Args:
        position: 位置
        model_type: 模型类型
        
    Returns:
        Dict: 解释摘要
    """
    try:
        explainer = get_explainer()
        if explainer is None:
            raise HTTPException(status_code=503, detail="SHAP解释器不可用")
        
        # 验证参数
        if position not in ["hundreds", "tens", "units"]:
            raise HTTPException(status_code=400, detail="位置参数必须是 hundreds, tens, 或 units")
        
        # 获取解释摘要
        summary = explainer.get_explanation_summary(
            position=position,
            model_type=model_type
        )
        
        return {
            "status": "success",
            "position": position,
            "model_type": model_type,
            "summary": summary,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"获取解释摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取解释摘要失败: {str(e)}")

@router.get("/models/available", summary="获取可用的模型类型")
async def get_available_models():
    """
    获取可用的模型类型列表
    
    Returns:
        Dict: 可用模型列表
    """
    try:
        explainer = get_explainer()
        if explainer is None:
            return {
                "status": "unavailable",
                "message": "SHAP解释器不可用",
                "models": []
            }
        
        # 获取可用模型
        available_models = ["xgboost", "lightgbm", "lstm", "ensemble"]
        
        return {
            "status": "success",
            "models": available_models,
            "default_model": "xgboost",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logging.error(f"获取可用模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取可用模型失败: {str(e)}")

@router.get("/health", summary="SHAP服务健康检查")
async def shap_health_check():
    """
    SHAP服务健康检查
    
    Returns:
        Dict: 健康状态
    """
    try:
        explainer = get_explainer()
        
        health_status = {
            "service": "SHAP解释服务",
            "status": "healthy" if explainer is not None else "unhealthy",
            "shap_available": explainer is not None,
            "timestamp": datetime.now().isoformat()
        }
        
        if explainer is not None:
            explainer_status = explainer.get_explainer_status()
            health_status.update({
                "feature_interfaces": explainer_status.get("feature_interfaces_count", 0),
                "supported_positions": explainer_status.get("positions", [])
            })
        
        return health_status
        
    except Exception as e:
        logging.error(f"SHAP健康检查失败: {e}")
        return {
            "service": "SHAP解释服务",
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
