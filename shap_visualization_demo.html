
        <!DOCTYPE html>
        <html>
        <head>
            <title>SHAP预测解释示例</title>
            <meta charset="utf-8">
            
        <style>
        .prediction-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .prediction-number {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }
        
        .confidence-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .confidence-high { background-color: #28a745; }
        .confidence-medium { background-color: #ffc107; color: #212529; }
        .confidence-low { background-color: #dc3545; }
        
        .feature-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        
        .feature-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        
        .strength-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .impact-strong-positive { background-color: #28a745; color: white; }
        .impact-positive { background-color: #d4edda; color: #155724; }
        .impact-strong-negative { background-color: #dc3545; color: white; }
        .impact-negative { background-color: #f8d7da; color: #721c24; }
        
        .prediction-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin: 10px 0;
            background: white;
        }
        
        .rank-badge {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        
        .rank-first { background-color: #ffd700; color: #333; }
        .rank-second { background-color: #c0c0c0; color: #333; }
        .rank-third { background-color: #cd7f32; }
        .rank-other { background-color: #6c757d; }
        
        .combination {
            font-size: 1.5em;
            font-weight: bold;
            font-family: monospace;
        }
        
        .shap-summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .impact-bars {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .impact-bars > div {
            padding: 8px;
            border-radius: 4px;
        }
        
        .positive-impact { background-color: #d4edda; color: #155724; }
        .negative-impact { background-color: #f8d7da; color: #721c24; }
        .net-impact { background-color: #d1ecf1; color: #0c5460; }
        </style>
        
        </head>
        <body>
            <div class="container">
                <h1>福彩3D AI预测解释系统</h1>
                
                <h2>单个预测解释</h2>
                
            <div class="prediction-header">
                <h4>预测结果</h4>
                <div class="prediction-number">
                    <span class="number">7</span>
                    <span class="position">(hundreds位)</span>
                </div>
                <div class="confidence-badge confidence-high">
                    置信度: 82.0% (高)
                </div>
            </div>
            
                <div class="overall-explanation">
                    <h5>AI分析依据</h5>
                    <p>预测百位数字 7，置信度 82.0%（高）。基于 5 个关键特征的分析，特征分析强烈支持此预测。</p>
                </div>
                <div class="feature-explanations"><h5>关键特征分析</h5>
                    <div class="feature-item">
                        <div class="feature-header">
                            <span class="support-icon">✅</span>
                            <span class="feature-name">百位_频次_20</span>
                            <span class="strength-badge impact-strong-positive">强烈</span>
                        </div>
                        <div class="feature-description">百位_频次_20(值:0.80) 强烈支持预测7</div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-header">
                            <span class="support-icon">⚠️</span>
                            <span class="feature-name">百位_遗漏_10</span>
                            <span class="strength-badge impact-negative">轻微</span>
                        </div>
                        <div class="feature-description">百位_遗漏_10(值:5.00) 轻微反对预测7</div>
                    </div>
                    </div>
                <div class="shap-summary">
                    <h5>特征影响分析</h5>
                    <div class="impact-bars">
                        <div class="positive-impact">
                            <span>支持因素: 0.180</span>
                        </div>
                        <div class="negative-impact">
                            <span>反对因素: -0.050</span>
                        </div>
                        <div class="net-impact">
                            <span>综合影响: 0.130</span>
                        </div>
                    </div>
                </div>
                
                
                <h2>融合预测解释</h2>
                
            <div class="fusion-header">
                <h4>智能融合预测</h4>
                <div class="fusion-method">
                    <span>融合方法: adaptive_fusion</span>
                    <span>分析组合: 20 个</span>
                </div>
                <p class="fusion-summary">通过 adaptive_fusion 融合方法，分析了 20 个预测组合，推荐概率最高的前几个组合。</p>
            </div>
            <div class="top-predictions"><h5>推荐组合</h5>
                    <div class="prediction-item">
                        <div class="rank-badge rank-first">1</div>
                        <div class="combination">123</div>
                        <div class="probability confidence-high">85.0%</div>
                        <div class="explanation">组合 123 概率 85.0%，基于多模型融合分析得出。</div>
                    </div>
                    
                    <div class="prediction-item">
                        <div class="rank-badge rank-second">2</div>
                        <div class="combination">456</div>
                        <div class="probability confidence-high">78.0%</div>
                        <div class="explanation">组合 456 概率 78.0%，基于多模型融合分析得出。</div>
                    </div>
                    
                    <div class="prediction-item">
                        <div class="rank-badge rank-third">3</div>
                        <div class="combination">789</div>
                        <div class="probability confidence-high">72.0%</div>
                        <div class="explanation">组合 789 概率 72.0%，基于多模型融合分析得出。</div>
                    </div>
                    </div>
            </div>
        </body>
        </html>
        