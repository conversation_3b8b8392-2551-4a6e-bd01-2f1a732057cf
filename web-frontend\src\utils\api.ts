import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://127.0.0.1:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    console.log('API Request:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log('API Response:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('Response Error:', error.response?.status, error.response?.data)
    
    // 统一错误处理
    if (error.response?.status === 404) {
      console.error('API endpoint not found:', error.config?.url)
    } else if (error.response?.status === 500) {
      console.error('Server error:', error.response?.data)
    } else if (error.response?.status === 503) {
      console.error('Service unavailable:', error.response?.data)
    }
    
    return Promise.reject(error)
  }
)

// 通用API请求函数
export const apiRequest = async (
  url: string,
  config: AxiosRequestConfig = {}
): Promise<AxiosResponse> => {
  try {
    const response = await api.request({
      url,
      ...config,
    })
    return response
  } catch (error) {
    console.error('API Request failed:', error)
    throw error
  }
}

// GET请求
export const apiGet = async (
  url: string,
  params?: any,
  config: AxiosRequestConfig = {}
): Promise<AxiosResponse> => {
  return apiRequest(url, {
    method: 'GET',
    params,
    ...config,
  })
}

// POST请求
export const apiPost = async (
  url: string,
  data?: any,
  config: AxiosRequestConfig = {}
): Promise<AxiosResponse> => {
  return apiRequest(url, {
    method: 'POST',
    data,
    ...config,
  })
}

// PUT请求
export const apiPut = async (
  url: string,
  data?: any,
  config: AxiosRequestConfig = {}
): Promise<AxiosResponse> => {
  return apiRequest(url, {
    method: 'PUT',
    data,
    ...config,
  })
}

// DELETE请求
export const apiDelete = async (
  url: string,
  config: AxiosRequestConfig = {}
): Promise<AxiosResponse> => {
  return apiRequest(url, {
    method: 'DELETE',
    ...config,
  })
}

// SHAP相关API
export const shapAPI = {
  // 获取SHAP状态
  getStatus: () => apiGet('/api/shap/status'),
  
  // 解释单个预测
  explainPrediction: (predictionNumber: string, position: string, modelType: string = 'xgboost') =>
    apiPost('/api/shap/explain/prediction', null, {
      params: {
        prediction_number: predictionNumber,
        position,
        model_type: modelType,
      },
    }),
  
  // 获取特征重要性
  getFeatureImportance: (position: string, modelType: string = 'xgboost', topN: number = 10) =>
    apiGet(`/api/shap/explain/features/${position}`, {
      model_type: modelType,
      top_n: topN,
    }),
  
  // 批量解释预测
  explainBatch: (predictions: Array<{prediction_number: string}>, position: string, modelType: string = 'xgboost') =>
    apiPost('/api/shap/explain/batch', predictions, {
      params: {
        position,
        model_type: modelType,
      },
    }),
  
  // 获取解释摘要
  getSummary: (position: string, modelType: string = 'xgboost') =>
    apiGet(`/api/shap/explain/summary/${position}`, {
      model_type: modelType,
    }),
  
  // 获取可用模型
  getAvailableModels: () => apiGet('/api/shap/models/available'),
  
  // 健康检查
  healthCheck: () => apiGet('/api/shap/health'),
}

// 预测相关API
export const predictionAPI = {
  // 获取最新预测
  getLatest: () => apiGet('/api/prediction/latest'),
  
  // 获取历史预测
  getHistory: (limit?: number, offset?: number) =>
    apiGet('/api/prediction/history', { limit, offset }),
  
  // 获取预测统计
  getStatistics: () => apiGet('/api/prediction/statistics'),
  
  // 获取准确率趋势
  getTrends: (days?: number) => apiGet('/api/prediction/trends', { days }),
  
  // 获取概率分布
  getProbabilityDistribution: (position?: string) =>
    apiGet('/api/prediction/probability-distribution', { position }),
  
  // 获取性能对比
  getComparison: () => apiGet('/api/prediction/comparison'),
  
  // 获取仪表盘数据
  getDashboard: () => apiGet('/api/prediction/dashboard'),
}

// 监控相关API
export const monitoringAPI = {
  // 获取系统状态
  getStatus: () => apiGet('/api/monitoring/status'),
  
  // 获取性能指标
  getMetrics: () => apiGet('/api/monitoring/metrics'),
  
  // 获取优化任务
  getTasks: () => apiGet('/api/monitoring/tasks'),
  
  // 健康检查
  healthCheck: () => apiGet('/api/monitoring/health'),
  
  // 触发优化
  triggerOptimization: () => apiPost('/api/monitoring/trigger-optimization'),
}

// 优化控制相关API
export const optimizationAPI = {
  // 触发优化
  trigger: () => apiPost('/api/optimization/trigger'),
  
  // 获取配置
  getConfig: () => apiGet('/api/optimization/config'),
  
  // 更新配置
  updateConfig: (config: any) => apiPut('/api/optimization/config', config),
  
  // 系统诊断
  diagnose: () => apiGet('/api/optimization/diagnostics'),
  
  // 获取任务状态
  getTaskStatus: (taskId: string) => apiGet(`/api/optimization/tasks/${taskId}`),
  
  // 取消任务
  cancelTask: (taskId: string) => apiDelete(`/api/optimization/tasks/${taskId}`),
}

// 缓存相关API
export const cacheAPI = {
  // 获取缓存统计
  getStats: () => apiGet('/api/cache/stats'),
  
  // 清理缓存
  clear: () => apiPost('/api/cache/clear'),
  
  // 清理过期缓存
  cleanup: () => apiPost('/api/cache/cleanup'),
  
  // 缓存预热
  warmup: () => apiPost('/api/cache/warmup'),
  
  // 健康检查
  healthCheck: () => apiGet('/api/cache/health'),
  
  // 获取缓存键
  getKeys: () => apiGet('/api/cache/keys'),
  
  // 删除特定缓存键
  deleteKey: (key: string) => apiDelete(`/api/cache/key/${key}`),
  
  // 获取性能指标
  getPerformance: () => apiGet('/api/cache/performance'),
}

// 复盘相关API
export const reviewAPI = {
  // 手动复盘
  manual: () => apiPost('/api/review/manual'),
  
  // 获取复盘状态
  getStatus: () => apiGet('/api/review/status'),
  
  // 获取复盘历史
  getHistory: () => apiGet('/api/review/history'),
  
  // 根据问题获取复盘
  getByIssue: (issue: string) => apiGet(`/api/review/history/${issue}`),
  
  // 测试复盘组件
  test: () => apiPost('/api/review/test'),
  
  // 获取复盘配置
  getConfig: () => apiGet('/api/review/config'),
  
  // 获取下次复盘计划
  getNextSchedule: () => apiGet('/api/review/next-schedule'),
}

export default api
