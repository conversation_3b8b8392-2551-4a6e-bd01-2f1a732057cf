#!/usr/bin/env python3
"""
测试SHAP可视化组件
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.web.components.shap_visualization import SHAPVisualizationComponent

def test_shap_visualization():
    """测试SHAP可视化组件"""
    print("=== 测试SHAP可视化组件 ===")
    
    try:
        # 1. 初始化可视化组件
        print("1. 初始化SHAP可视化组件...")
        viz_component = SHAPVisualizationComponent()
        print("✓ SHAP可视化组件初始化成功")
        
        # 2. 测试预测解释格式化
        print("2. 测试预测解释格式化...")
        mock_explanation = {
            'predicted_number': 7,
            'confidence': 0.82,
            'position': 'hundreds',
            'overall_explanation': '预测百位数字 7，置信度 82.0%（高）。基于 5 个关键特征的分析，特征分析强烈支持此预测。',
            'feature_explanations': [
                {
                    'feature_name': '百位_频次_20',
                    'shap_value': 0.15,
                    'feature_value': 0.8,
                    'support_type': '支持',
                    'strength': '强烈',
                    'description': '百位_频次_20(值:0.80) 强烈支持预测7'
                },
                {
                    'feature_name': '百位_遗漏_10',
                    'shap_value': -0.03,
                    'feature_value': 5.0,
                    'support_type': '反对',
                    'strength': '轻微',
                    'description': '百位_遗漏_10(值:5.00) 轻微反对预测7'
                }
            ],
            'shap_summary': {
                'total_positive_impact': 0.18,
                'total_negative_impact': -0.05,
                'net_support': 0.13
            },
            'explanation_type': 'shap_based'
        }
        
        formatted_explanation = viz_component.format_prediction_explanation(mock_explanation)
        print("✓ 预测解释格式化成功")
        print(f"  预测数字: {formatted_explanation['prediction_info']['number']}")
        print(f"  置信度: {formatted_explanation['prediction_info']['confidence']:.1%}")
        print(f"  特征数量: {len(formatted_explanation['features'])}")
        
        # 3. 测试融合解释格式化
        print("3. 测试融合解释格式化...")
        mock_fusion_explanation = {
            'fusion_method': 'adaptive_fusion',
            'total_predictions': 20,
            'explained_predictions': [
                {'rank': 1, 'combination': '123', 'probability': 0.85, 'explanation': '组合 123 概率 85.0%，基于多模型融合分析得出。'},
                {'rank': 2, 'combination': '456', 'probability': 0.78, 'explanation': '组合 456 概率 78.0%，基于多模型融合分析得出。'},
                {'rank': 3, 'combination': '789', 'probability': 0.72, 'explanation': '组合 789 概率 72.0%，基于多模型融合分析得出。'}
            ],
            'summary': '通过 adaptive_fusion 融合方法，分析了 20 个预测组合，推荐概率最高的前几个组合。'
        }
        
        formatted_fusion = viz_component.format_fusion_explanation(mock_fusion_explanation)
        print("✓ 融合解释格式化成功")
        print(f"  融合方法: {formatted_fusion['fusion_info']['method']}")
        print(f"  推荐数量: {len(formatted_fusion['top_predictions'])}")
        
        # 4. 测试HTML生成
        print("4. 测试HTML生成...")
        explanation_html = viz_component.generate_explanation_html(formatted_explanation)
        fusion_html = viz_component.generate_fusion_html(formatted_fusion)
        
        print("✓ HTML生成成功")
        print(f"  解释HTML长度: {len(explanation_html)} 字符")
        print(f"  融合HTML长度: {len(fusion_html)} 字符")
        
        # 5. 测试CSS样式
        print("5. 测试CSS样式...")
        css_styles = viz_component.get_css_styles()
        print("✓ CSS样式获取成功")
        print(f"  CSS长度: {len(css_styles)} 字符")
        
        # 6. 保存示例HTML文件
        print("6. 生成示例HTML文件...")
        sample_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>SHAP预测解释示例</title>
            <meta charset="utf-8">
            {css_styles}
        </head>
        <body>
            <div class="container">
                <h1>福彩3D AI预测解释系统</h1>
                
                <h2>单个预测解释</h2>
                {explanation_html}
                
                <h2>融合预测解释</h2>
                {fusion_html}
            </div>
        </body>
        </html>
        """
        
        with open('shap_visualization_demo.html', 'w', encoding='utf-8') as f:
            f.write(sample_html)
        
        print("✓ 示例HTML文件已生成: shap_visualization_demo.html")
        
        print("\n=== SHAP可视化组件测试完成 ===")
        print("✓ 所有功能测试通过")
        print("🎨 可视化组件已准备就绪，可集成到Web界面")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_shap_visualization()
    sys.exit(0 if success else 1)
