#!/usr/bin/env python3
"""
磁盘空间清理脚本
安全地清理项目中的临时文件和缓存
"""

import os
import shutil
import sys
from pathlib import Path
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DiskCleaner:
    """磁盘清理器"""
    
    def __init__(self, project_root: str = None):
        """初始化清理器"""
        if project_root is None:
            self.project_root = Path(__file__).parent.parent
        else:
            self.project_root = Path(project_root)
        
        self.cleaned_size = 0
        self.cleaned_files = 0
        
    def get_directory_size(self, path: Path) -> int:
        """获取目录大小（字节）"""
        total_size = 0
        try:
            for file_path in path.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except (OSError, PermissionError):
            pass
        return total_size
    
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.2f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.2f} TB"
    
    def clean_pycache(self):
        """清理Python缓存文件"""
        logger.info("🧹 清理Python缓存文件...")
        
        pycache_dirs = list(self.project_root.rglob('__pycache__'))
        
        for pycache_dir in pycache_dirs:
            try:
                size_before = self.get_directory_size(pycache_dir)
                shutil.rmtree(pycache_dir)
                self.cleaned_size += size_before
                self.cleaned_files += 1
                logger.info(f"  ✅ 删除: {pycache_dir.relative_to(self.project_root)}")
            except Exception as e:
                logger.warning(f"  ⚠️ 无法删除 {pycache_dir}: {e}")
    
    def clean_pyc_files(self):
        """清理.pyc文件"""
        logger.info("🧹 清理.pyc文件...")
        
        pyc_files = list(self.project_root.rglob('*.pyc'))
        
        for pyc_file in pyc_files:
            try:
                size_before = pyc_file.stat().st_size
                pyc_file.unlink()
                self.cleaned_size += size_before
                self.cleaned_files += 1
                logger.info(f"  ✅ 删除: {pyc_file.relative_to(self.project_root)}")
            except Exception as e:
                logger.warning(f"  ⚠️ 无法删除 {pyc_file}: {e}")
    
    def clean_logs(self, keep_days: int = 7):
        """清理旧日志文件"""
        logger.info(f"🧹 清理{keep_days}天前的日志文件...")
        
        logs_dir = self.project_root / 'logs'
        if not logs_dir.exists():
            logger.info("  ℹ️ 日志目录不存在")
            return
        
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        
        for log_file in logs_dir.glob('*.log'):
            try:
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_date:
                    size_before = log_file.stat().st_size
                    log_file.unlink()
                    self.cleaned_size += size_before
                    self.cleaned_files += 1
                    logger.info(f"  ✅ 删除旧日志: {log_file.name}")
            except Exception as e:
                logger.warning(f"  ⚠️ 无法删除 {log_file}: {e}")
    
    def clean_temp_files(self):
        """清理临时文件"""
        logger.info("🧹 清理临时文件...")
        
        temp_patterns = [
            '*.tmp',
            '*.temp',
            '*~',
            '.DS_Store',
            'Thumbs.db',
            '*.bak',
            '*.backup'
        ]
        
        for pattern in temp_patterns:
            temp_files = list(self.project_root.rglob(pattern))
            for temp_file in temp_files:
                try:
                    size_before = temp_file.stat().st_size
                    temp_file.unlink()
                    self.cleaned_size += size_before
                    self.cleaned_files += 1
                    logger.info(f"  ✅ 删除临时文件: {temp_file.relative_to(self.project_root)}")
                except Exception as e:
                    logger.warning(f"  ⚠️ 无法删除 {temp_file}: {e}")
    
    def clean_node_modules_cache(self):
        """清理Node.js缓存"""
        logger.info("🧹 清理Node.js缓存...")
        
        frontend_dir = self.project_root / 'web-frontend'
        if not frontend_dir.exists():
            logger.info("  ℹ️ 前端目录不存在")
            return
        
        # 清理.vite缓存
        vite_cache = frontend_dir / 'node_modules' / '.vite'
        if vite_cache.exists():
            try:
                size_before = self.get_directory_size(vite_cache)
                shutil.rmtree(vite_cache)
                self.cleaned_size += size_before
                self.cleaned_files += 1
                logger.info(f"  ✅ 删除Vite缓存: {self.format_size(size_before)}")
            except Exception as e:
                logger.warning(f"  ⚠️ 无法删除Vite缓存: {e}")
        
        # 清理dist目录
        dist_dir = frontend_dir / 'dist'
        if dist_dir.exists():
            try:
                size_before = self.get_directory_size(dist_dir)
                shutil.rmtree(dist_dir)
                self.cleaned_size += size_before
                self.cleaned_files += 1
                logger.info(f"  ✅ 删除构建文件: {self.format_size(size_before)}")
            except Exception as e:
                logger.warning(f"  ⚠️ 无法删除构建文件: {e}")
    
    def clean_model_cache(self):
        """清理模型缓存文件"""
        logger.info("🧹 清理模型缓存...")
        
        cache_dirs = [
            'models/cache',
            'data/cache',
            '.cache'
        ]
        
        for cache_dir_name in cache_dirs:
            cache_dir = self.project_root / cache_dir_name
            if cache_dir.exists():
                try:
                    size_before = self.get_directory_size(cache_dir)
                    shutil.rmtree(cache_dir)
                    self.cleaned_size += size_before
                    self.cleaned_files += 1
                    logger.info(f"  ✅ 删除缓存目录: {cache_dir_name} ({self.format_size(size_before)})")
                except Exception as e:
                    logger.warning(f"  ⚠️ 无法删除 {cache_dir}: {e}")
    
    def clean_debug_files(self):
        """清理调试文件"""
        logger.info("🧹 清理调试文件...")
        
        debug_dir = self.project_root / 'debug'
        if not debug_dir.exists():
            logger.info("  ℹ️ 调试目录不存在")
            return
        
        # 保留最新的调试报告，删除旧的
        debug_files = list(debug_dir.glob('*.md'))
        if len(debug_files) > 3:  # 保留最新的3个
            debug_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            for old_file in debug_files[3:]:
                try:
                    size_before = old_file.stat().st_size
                    old_file.unlink()
                    self.cleaned_size += size_before
                    self.cleaned_files += 1
                    logger.info(f"  ✅ 删除旧调试文件: {old_file.name}")
                except Exception as e:
                    logger.warning(f"  ⚠️ 无法删除 {old_file}: {e}")
    
    def run_cleanup(self, aggressive: bool = False):
        """运行清理"""
        logger.info("🚀 开始磁盘清理...")
        
        # 基础清理
        self.clean_pycache()
        self.clean_pyc_files()
        self.clean_temp_files()
        self.clean_logs(keep_days=7)
        self.clean_debug_files()
        
        # 前端清理
        self.clean_node_modules_cache()
        
        # 激进清理
        if aggressive:
            logger.info("⚡ 执行激进清理...")
            self.clean_model_cache()
            self.clean_logs(keep_days=1)  # 只保留1天的日志
        
        # 显示清理结果
        logger.info("✅ 清理完成!")
        logger.info(f"📊 清理统计:")
        logger.info(f"  - 清理文件数: {self.cleaned_files}")
        logger.info(f"  - 释放空间: {self.format_size(self.cleaned_size)}")

def main():
    """主函数"""
    print("=== 磁盘空间清理工具 ===")
    
    # 检查命令行参数
    aggressive = '--aggressive' in sys.argv or '-a' in sys.argv
    
    if aggressive:
        print("⚡ 激进清理模式")
        confirm = input("确认要执行激进清理吗？这将删除更多文件 (y/N): ")
        if confirm.lower() != 'y':
            print("❌ 取消清理")
            return
    
    # 执行清理
    cleaner = DiskCleaner()
    cleaner.run_cleanup(aggressive=aggressive)
    
    print("\n=== 清理完成 ===")

if __name__ == "__main__":
    main()
