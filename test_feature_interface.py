#!/usr/bin/env python3
"""
测试特征接口功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.predictors.feature_interface import PredictorFeatureInterface

def test_feature_interface():
    """测试特征接口"""
    print("=== 测试特征接口 ===")
    
    try:
        # 测试百位特征接口
        print("1. 初始化百位特征接口...")
        hundreds_interface = PredictorFeatureInterface('hundreds')
        print("✓ 百位特征接口初始化成功")
        
        # 测试特征名称获取
        print("2. 获取特征名称...")
        feature_names = hundreds_interface.get_feature_names()
        print(f"✓ 获取到 {len(feature_names)} 个特征名称")
        print(f"前10个特征: {feature_names[:10]}")
        
        # 测试特征生成
        print("3. 测试特征生成...")
        test_issue = "2025210"
        features = hundreds_interface.get_prediction_features(test_issue, 50)
        print(f"✓ 成功生成 {len(features)} 个特征")
        print(f"特征样例: {features[:5]}")
        
        # 测试十位特征接口
        print("4. 测试十位特征接口...")
        tens_interface = PredictorFeatureInterface('tens')
        tens_features = tens_interface.get_prediction_features(test_issue, 50)
        print(f"✓ 十位特征生成成功，数量: {len(tens_features)}")
        
        # 测试个位特征接口
        print("5. 测试个位特征接口...")
        units_interface = PredictorFeatureInterface('units')
        units_features = units_interface.get_prediction_features(test_issue, 50)
        print(f"✓ 个位特征生成成功，数量: {len(units_features)}")
        
        print("\n=== 特征接口测试完成 ===")
        print("✓ 所有测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_feature_interface()
    sys.exit(0 if success else 1)
